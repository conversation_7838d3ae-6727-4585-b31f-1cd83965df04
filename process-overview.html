<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>整体验收流程概览及核心瓶颈</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js">
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #87CEEB 0%, #E6F3FF 50%, #87CEEB 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* 装饰性虚线弧形 */
        .decorative-arc {
            position: absolute;
            width: 120%;
            height: 200px;
            border: 3px dashed #ffffff;
            border-radius: 50%;
            opacity: 0.7;
            animation: float 8s ease-in-out infinite;
        }

        .arc-top {
            top: -100px;
            left: -10%;
            border-bottom: none;
        }

        .arc-bottom {
            bottom: -100px;
            left: -10%;
            border-top: none;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-8px) rotate(1deg); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
            position: relative;
            z-index: 10;
        }

        .content-layout {
            display: flex;
            gap: 40px;
            align-items: flex-start;
        }

        .main-content {
            flex: 2;
        }

        .chart-sidebar {
            flex: 1;
            min-width: 350px;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            animation: slideInRight 1.5s ease-out 1.5s both;
            margin-bottom: 20px;
        }

        .chart-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #2B6CB0;
            margin-bottom: 20px;
        }

        .export-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 15px 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            animation: bounceIn 1s ease-out 2.5s both;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .export-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
        }

        .export-button:active {
            transform: translateY(-1px);
        }

        .export-button::before {
            content: '📊';
            margin-right: 8px;
        }

        .main-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            animation: fadeInUp 1s ease-out;
        }

        .main-title {
            text-align: center;
            font-size: 32px;
            font-weight: bold;
            color: #2B6CB0;
            margin-bottom: 15px;
            animation: fadeInDown 1.2s ease-out;
        }

        .title-underline {
            width: 80%;
            height: 3px;
            background: linear-gradient(90deg, #4299E1, #63B3ED);
            margin: 0 auto 40px;
            border-radius: 2px;
            animation: expandWidth 1.5s ease-out 0.5s both;
        }

        .cycle-highlight {
            text-align: center;
            font-size: 28px;
            margin-bottom: 50px;
            color: #2D3748;
            animation: fadeIn 1.5s ease-out 1s both;
        }

        .cycle-number {
            color: #E53E3E;
            font-weight: bold;
            font-size: 36px;
        }

        .process-flow {
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin: 60px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .process-step {
            flex: 1;
            min-width: 200px;
            text-align: center;
            position: relative;
            animation: slideInUp 1s ease-out;
        }

        .process-step:nth-child(1) { animation-delay: 0.2s; }
        .process-step:nth-child(2) { animation-delay: 0.4s; }
        .process-step:nth-child(3) { animation-delay: 0.6s; }
        .process-step:nth-child(4) { animation-delay: 0.8s; }
        .process-step:nth-child(5) { animation-delay: 1.0s; }
        .process-step:nth-child(6) { animation-delay: 1.2s; }

        .step-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            font-weight: bold;
            position: relative;
            transition: transform 0.3s ease;
        }

        .step-icon:hover {
            transform: scale(1.1) rotate(5deg);
        }

        .step1 .step-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .step2 .step-icon { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .step3 .step-icon { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .step4 .step-icon { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .step5 .step-icon { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .step6 .step-icon { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #2D3748; }

        .step-title {
            font-size: 16px;
            font-weight: bold;
            color: #2B6CB0;
            margin-bottom: 8px;
        }

        .step-duration {
            font-size: 14px;
            color: #E53E3E;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .step-description {
            font-size: 12px;
            color: #4A5568;
            line-height: 1.4;
            padding: 0 10px;
        }

        /* 连接线 */
        .process-step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 40px;
            right: -50px;
            width: 40px;
            height: 2px;
            background: linear-gradient(90deg, #4299E1, #63B3ED);
            z-index: 1;
            animation: drawLine 0.5s ease-out;
        }

        .process-step:nth-child(1)::after { animation-delay: 1.4s; }
        .process-step:nth-child(2)::after { animation-delay: 1.6s; }
        .process-step:nth-child(3)::after { animation-delay: 1.8s; }
        .process-step:nth-child(4)::after { animation-delay: 2.0s; }
        .process-step:nth-child(5)::after { animation-delay: 2.2s; }

        /* 动画关键帧 */
        @keyframes fadeInUp {
            0% { opacity: 0; transform: translateY(30px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInDown {
            0% { opacity: 0; transform: translateY(-30px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }

        @keyframes slideInUp {
            0% { opacity: 0; transform: translateY(50px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        @keyframes expandWidth {
            0% { width: 0; }
            100% { width: 80%; }
        }

        @keyframes drawLine {
            0% { width: 0; }
            100% { width: 40px; }
        }

        @keyframes bounceIn {
            0% { opacity: 0; transform: scale(0.3); }
            50% { opacity: 1; transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { opacity: 1; transform: scale(1); }
        }

        @keyframes slideInRight {
            0% { opacity: 0; transform: translateX(50px); }
            100% { opacity: 1; transform: translateX(0); }
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .process-flow {
                flex-direction: column;
                align-items: center;
            }
            
            .process-step {
                margin-bottom: 30px;
                max-width: 300px;
            }
            
            .process-step:not(:last-child)::after {
                display: none;
            }
        }

        .page-number {
            position: absolute;
            bottom: 20px;
            right: 30px;
            font-size: 18px;
            font-weight: bold;
            color: #2B6CB0;
            animation: fadeIn 2s ease-out 2.5s both;
        }
    </style>
</head>
<body>
    <!-- 装饰性弧形 -->
    <div class="decorative-arc arc-top"></div>
    <div class="decorative-arc arc-bottom"></div>

    <div class="container">
        <div class="content-layout">
            <!-- 主要内容区域 -->
            <div class="main-content">
                <div class="main-card">
                    <!-- 主标题 -->
                    <div class="main-title">整体验收流程概览及核心瓶颈</div>
                    <div class="title-underline"></div>

                    <!-- 周期突出显示 -->
                    <div class="cycle-highlight">
                        垫款周期约<span class="cycle-number">7-11</span>个月
                    </div>

                    <!-- 流程步骤 -->
                    <div class="process-flow">
                <div class="process-step step1">
                    <div class="step-icon">1</div>
                    <div class="step-title">筹备执行期</div>
                    <div class="step-duration">1-3个月</div>
                    <div class="step-description">
                        项目多专业装配明细清单<br>
                        长达1-3个月
                    </div>
                </div>

                <div class="process-step step2">
                    <div class="step-icon">2</div>
                    <div class="step-title">数据回收期</div>
                    <div class="step-duration">15-30天</div>
                    <div class="step-description">
                        数据回收过程中存在内容<br>
                        内容存在约1个月
                    </div>
                </div>

                <div class="process-step step3">
                    <div class="step-icon">3</div>
                    <div class="step-title">结算产出筹备</div>
                    <div class="step-duration">15-30天</div>
                    <div class="step-description">
                        结算筹备及核算，筹算收<br>
                        据出具大概约1个月
                    </div>
                </div>

                <div class="process-step step4">
                    <div class="step-icon">4</div>
                    <div class="step-title">提交验收→结算验收</div>
                    <div class="step-duration">2-4个月</div>
                    <div class="step-description">
                        周期最长，问题最多！<br>
                        缺乏问题，重复问题，流程<br>
                        复杂工具缺乏自动化等7步<br>
                        反复的重复性
                    </div>
                </div>

                <div class="process-step step5">
                    <div class="step-icon">5</div>
                    <div class="step-title">验收→通知开票</div>
                    <div class="step-duration">1个月</div>
                    <div class="step-description">
                        流程不透明，进度不可知<br>
                        缺乏问题，内部审计流程<br>
                        收"后台黑盒"，缺乏透明<br>
                        度与信息化，有待优化
                    </div>
                </div>

                <div class="process-step step6">
                    <div class="step-icon">6</div>
                    <div class="step-title">账交发票→实际付款</div>
                    <div class="step-duration">15-35天</div>
                    <div class="step-description">
                        周期清晰，流程可控<br>
                        缺乏问题，有工作提醒流程
                    </div>
                    </div>
                </div>
            </div>

            <!-- 侧边栏：图表和导出 -->
            <div class="chart-sidebar">
                <!-- 饼图容器 -->
                <div class="chart-container">
                    <div class="chart-title">时间周期分布</div>
                    <canvas id="timeDistributionChart" width="300" height="300"></canvas>
                    <!--
                    饼图数据 JSON:
                    {
                      "labels": ["筹备执行期", "数据回收期", "结算产出筹备", "提交验收→结算验收", "验收→通知开票", "账交发票→实际付款"],
                      "datasets": [{
                        "label": "时间分布",
                        "data": [90, 22.5, 22.5, 90, 30, 25],
                        "backgroundColor": [
                          "#667eea",
                          "#f093fb",
                          "#4facfe",
                          "#43e97b",
                          "#fa709a",
                          "#a8edea"
                        ],
                        "borderWidth": 2,
                        "borderColor": "#ffffff"
                      }]
                    }
                    -->
                </div>

                <!-- 导出按钮 -->
                <button class="export-button" onclick="exportToPPT()">
                    导出为 PowerPoint
                </button>
            </div>
        </div>

        <!-- 页码 -->
        <div class="page-number">45</div>
    </div>

    <script>
        // 初始化饼图
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('timeDistributionChart').getContext('2d');

            setTimeout(() => {
                new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: ['筹备执行期', '数据回收期', '结算产出筹备', '提交验收→结算验收', '验收→通知开票', '账交发票→实际付款'],
                        datasets: [{
                            label: '时间分布(天)',
                            data: [90, 22.5, 22.5, 90, 30, 25], // 按平均天数计算
                            backgroundColor: [
                                '#667eea',
                                '#f093fb',
                                '#4facfe',
                                '#43e97b',
                                '#fa709a',
                                '#a8edea'
                            ],
                            borderWidth: 3,
                            borderColor: '#ffffff',
                            hoverOffset: 10
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 15,
                                    font: {
                                        size: 11
                                    },
                                    usePointStyle: true
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.parsed;
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((value / total) * 100).toFixed(1);
                                        return `${label}: ${value}天 (${percentage}%)`;
                                    }
                                }
                            }
                        },
                        animation: {
                            animateRotate: true,
                            duration: 2000
                        }
                    }
                });
            }, 2000);
        });

        // PPT导出功能
        function exportToPPT() {
            // 创建可编辑的数据结构
            const presentationData = {
                title: "整体验收流程概览及核心瓶颈",
                subtitle: "时间周期分析报告",
                processSteps: [
                    {
                        step: 1,
                        title: "筹备执行期",
                        duration: "1-3个月",
                        description: "项目多专业装配明细清单长达1-3个月",
                        avgDays: 90,
                        color: "#667eea"
                    },
                    {
                        step: 2,
                        title: "数据回收期",
                        duration: "15-30天",
                        description: "数据回收过程中存在内容内容存在约1个月",
                        avgDays: 22.5,
                        color: "#f093fb"
                    },
                    {
                        step: 3,
                        title: "结算产出筹备",
                        duration: "15-30天",
                        description: "结算筹备及核算，筹算收据出具大概约1个月",
                        avgDays: 22.5,
                        color: "#4facfe"
                    },
                    {
                        step: 4,
                        title: "提交验收→结算验收",
                        duration: "2-4个月",
                        description: "周期最长，问题最多！缺乏问题，重复问题，流程复杂工具缺乏自动化等7步反复的重复性",
                        avgDays: 90,
                        color: "#43e97b",
                        isBottleneck: true
                    },
                    {
                        step: 5,
                        title: "验收→通知开票",
                        duration: "1个月",
                        description: "流程不透明，进度不可知缺乏问题，内部审计流程收"后台黑盒"，缺乏透明度与信息化，有待优化",
                        avgDays: 30,
                        color: "#fa709a"
                    },
                    {
                        step: 6,
                        title: "账交发票→实际付款",
                        duration: "15-35天",
                        description: "周期清晰，流程可控缺乏问题，有工作提醒流程",
                        avgDays: 25,
                        color: "#a8edea"
                    }
                ],
                chartData: {
                    labels: ["筹备执行期", "数据回收期", "结算产出筹备", "提交验收→结算验收", "验收→通知开票", "账交发票→实际付款"],
                    values: [90, 22.5, 22.5, 90, 30, 25],
                    colors: ["#667eea", "#f093fb", "#4facfe", "#43e97b", "#fa709a", "#a8edea"]
                },
                totalCycle: "7-11个月",
                pageNumber: 45
            };

            // 创建下载链接
            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(presentationData, null, 2));
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", dataStr);
            downloadAnchorNode.setAttribute("download", "process_overview_data.json");
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();

            // 显示导出说明
            alert(`📊 数据已导出为 JSON 格式！

使用说明：
1. 下载的 JSON 文件包含所有文字、数据和颜色信息
2. 可以将数据导入到 PowerPoint 中重新创建图表
3. 所有文字内容都可以直接复制粘贴到 PPT 中
4. 颜色代码可用于保持视觉一致性

💡 建议：
- 在 PowerPoint 中插入饼图，使用提供的数据和颜色
- 复制流程步骤文字到 PPT 文本框
- 使用相同的颜色方案保持设计一致性`);
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>2025 Q2 销售业绩回顾与展望</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/theme/black.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/plugin/highlight/monokai.min.css">
    <style>
        .reveal .slides section {
            text-align: left;
        }
        .reveal h1, .reveal h2, .reveal h3 {
            text-align: center;
        }
        .two-column {
            display: flex;
            align-items: flex-start;
            gap: 2rem;
        }
        .two-column .left {
            flex: 1;
        }
        .two-column .right {
            flex: 1;
        }
        .center-content {
            text-align: center;
        }
        .title-slide {
            text-align: center;
        }
        .section-header {
            text-align: center;
            background: linear-gradient(45deg, #1e3c72, #2a5298);
        }
        .thank-you {
            text-align: center;
        }
        canvas {
            max-width: 100%;
            height: auto;
        }
        svg {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="reveal">
        <div class="slides">
            <!-- 第1页: 首页 -->
            <section class="title-slide">
                <h1>2025 Q2 销售业绩回顾与展望</h1>
                <h3>市场部业绩汇报</h3>
                <p>汇报人：张三</p>
                <p><small>2025年8月30日</small></p>
            </section>

            <!-- 第2页: 大纲 -->
            <section>
                <h2>本次汇报大纲</h2>
                <ol>
                    <li>Q2 业绩总体回顾</li>
                    <li>关键渠道表现分析</li>
                    <li>成功案例分享与挑战</li>
                    <li>Q3 目标与策略规划</li>
                </ol>
            </section>

            <!-- 第3页: 过渡页 -->
            <section class="section-header">
                <h1>第一部分：Q2 业绩总体回顾</h1>
            </section>

            <!-- 第4页: 内容页 (图文) -->
            <section>
                <h2>Q2 总体销售额达成情况</h2>
                <div class="two-column">
                    <div class="left">
                        <ul>
                            <li>总销售额达到 1.2 亿元，同比增长 15%</li>
                            <li>完成季度目标的 110%</li>
                            <li>利润率稳定在 12%</li>
                        </ul>
                    </div>
                    <div class="right">
                        <canvas id="salesChart" width="400" height="300"></canvas>
                        <!--
                        {
                          "labels": ["第一季度", "第二季度"],
                          "datasets": [{
                            "label": "销售额 (万元)",
                            "data": [10500, 12000],
                            "backgroundColor": ["rgba(255, 99, 132, 0.6)", "rgba(54, 162, 235, 0.6)"]
                          }]
                        }
                        -->
                    </div>
                </div>
            </section>

            <!-- 第5页: 内容页 (矢量图) -->
            <section>
                <h2>我们的核心销售流程</h2>
                <div class="center-content">
                    <svg width="600" height="200" viewBox="0 0 600 200" xmlns="http://www.w3.org/2000/svg">
                        <!-- 步骤1: 线索获取 -->
                        <rect x="50" y="75" width="120" height="50" rx="10" fill="#4CAF50" stroke="#fff" stroke-width="2"/>
                        <text x="110" y="105" text-anchor="middle" fill="white" font-family="Arial" font-size="14">线索获取</text>
                        
                        <!-- 箭头1 -->
                        <path d="M180 100 L220 100" stroke="#fff" stroke-width="3" marker-end="url(#arrowhead)"/>
                        
                        <!-- 步骤2: 需求分析 -->
                        <rect x="240" y="75" width="120" height="50" rx="10" fill="#2196F3" stroke="#fff" stroke-width="2"/>
                        <text x="300" y="105" text-anchor="middle" fill="white" font-family="Arial" font-size="14">需求分析</text>
                        
                        <!-- 箭头2 -->
                        <path d="M370 100 L410 100" stroke="#fff" stroke-width="3" marker-end="url(#arrowhead)"/>
                        
                        <!-- 步骤3: 签约成交 -->
                        <rect x="430" y="75" width="120" height="50" rx="10" fill="#FF9800" stroke="#fff" stroke-width="2"/>
                        <text x="490" y="105" text-anchor="middle" fill="white" font-family="Arial" font-size="14">签约成交</text>
                        
                        <!-- 箭头标记定义 -->
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#fff"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
            </section>

            <!-- 第6页: 内容页 (代码示例) -->
            <section>
                <h2>数据查询脚本示例</h2>
                <pre><code class="python" data-line-numbers>import pandas as pd

def get_sales_data(quarter):
    # Pretend to query a database
    if quarter == "Q2":
        return {"month": ["Apr", "May", "Jun"], "sales": [3800, 4200, 4000]}
    return None

q2_data = get_sales_data("Q2")
df = pd.DataFrame(q2_data)
print(df.head())</code></pre>
            </section>

            <!-- 第7页: 过渡页 -->
            <section class="section-header">
                <h1>第四部分：Q3 目标与策略规划</h1>
            </section>

            <!-- 第8页: 结尾页 -->
            <section class="thank-you">
                <h1>Q&A</h1>
                <p><small>感谢聆听！</small></p>
            </section>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/plugin/highlight/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    
    <script>
        Reveal.initialize({
            hash: true,
            controls: true,
            progress: true,
            center: true,
            transition: 'slide',
            width: 1280,
            height: 720,
            plugins: [ RevealHighlight ]
        });

        // 初始化图表
        Reveal.addEventListener('ready', function() {
            const ctx = document.getElementById('salesChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['第一季度', '第二季度'],
                    datasets: [{
                        label: '销售额 (万元)',
                        data: [10500, 12000],
                        backgroundColor: ['rgba(255, 99, 132, 0.6)', 'rgba(54, 162, 235, 0.6)'],
                        borderColor: ['rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Q1 vs Q2 销售额对比',
                            color: '#fff'
                        },
                        legend: {
                            labels: {
                                color: '#fff'
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: '#fff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: '#fff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>